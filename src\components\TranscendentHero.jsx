'use client';

import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

const TranscendentHero = ({ 
  videoSrc = '/videos/bali-hero.mp4', 
  fallbackImage = '/images/background/bali-hero.webp',
  overlayOpacity = 0.3 
}) => {
  const videoRef = useRef(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedData = () => {
      setIsVideoLoaded(true);
      // Slow down video to 0.7x speed for more meditative feel
      video.playbackRate = 0.7;
    };

    const handleError = () => {
      setHasError(true);
      console.log('Video failed to load, using fallback image');
    };

    const handleCanPlay = () => {
      video.play().catch(() => {
        // Autoplay failed, that's okay
        setHasError(true);
      });
    };

    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('error', handleError);
    video.addEventListener('canplay', handleCanPlay);

    return () => {
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('error', handleError);
      video.removeEventListener('canplay', handleCanPlay);
    };
  }, []);

  return (
    <section className="relative h-screen overflow-hidden">
      {/* Video Background */}
      {!hasError && (
        <motion.video
          ref={videoRef}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ 
            opacity: isVideoLoaded ? 1 : 0,
            scale: isVideoLoaded ? 1 : 1.1
          }}
          transition={{ duration: 2 }}
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
        >
          <source src={videoSrc} type="video/mp4" />
        </motion.video>
      )}

      {/* Fallback Image */}
      <motion.div
        initial={{ opacity: 1 }}
        animate={{ opacity: (isVideoLoaded && !hasError) ? 0 : 1 }}
        transition={{ duration: 2 }}
        className="absolute inset-0"
      >
        <Image
          src={fallbackImage}
          alt="Hero Background"
          fill
          priority
          className="object-cover"
          sizes="100vw"
          quality={90}
        />
      </motion.div>

      {/* Gradient Overlay */}
      <div 
        className="absolute inset-0 bg-gradient-to-b from-temple/30 via-temple/10 to-temple/30"
        style={{ opacity: overlayOpacity }}
      />

      {/* Breathing overlay effect */}
      <motion.div
        animate={{
          opacity: [0.1, 0.3, 0.1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute inset-0 bg-gradient-radial from-golden/10 via-transparent to-transparent"
      />

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -40, 0],
              opacity: [0.1, 0.4, 0.1],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 8 + i * 0.5,
              repeat: Infinity,
              delay: i * 0.7,
            }}
            className="absolute w-1 h-1 bg-golden/30 rounded-full"
            style={{
              left: `${10 + (i * 7)}%`,
              top: `${20 + (i % 4) * 20}%`,
            }}
          />
        ))}
      </div>

      {/* Sacred geometry overlay */}
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 120, repeat: Infinity, ease: "linear" }}
        className="absolute inset-0 flex items-center justify-center pointer-events-none"
      >
        <svg
          width="600"
          height="600"
          viewBox="0 0 600 600"
          className="opacity-5"
        >
          {/* Flower of Life pattern */}
          <g stroke="white" strokeWidth="1" fill="none">
            <circle cx="300" cy="300" r="45" />
            <circle cx="300" cy="255" r="45" />
            <circle cx="300" cy="345" r="45" />
            <circle cx="261" cy="277.5" r="45" />
            <circle cx="339" cy="277.5" r="45" />
            <circle cx="261" cy="322.5" r="45" />
            <circle cx="339" cy="322.5" r="45" />
          </g>
        </svg>
      </motion.div>

      {/* Hero Content */}
      <div className="relative z-10 h-full flex items-center justify-center">
        <div className="max-w-4xl mx-auto px-8 sm:px-12 lg:px-16 text-center">
          <div className="space-y-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
              className="subtitle mb-8 text-xs uppercase tracking-[0.3em] opacity-60 text-white"
            >
              Retreaty Jogowe
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1.2, delay: 0.7 }}
              className="text-4xl md:text-5xl lg:text-6xl font-serif font-light mb-12 tracking-tight leading-[0.95] text-white"
            >
              Bali Yoga<br />Journey
            </motion.h1>

            <div className="space-y-8">
              <motion.p
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 1 }}
                className="text-lg max-w-md mx-auto leading-relaxed font-light opacity-80 text-white"
              >
                Harmonia ducha i tropikalnej przygody
              </motion.p>

              {/* Subtelny separator */}
              <motion.div
                initial={{ opacity: 0, scaleX: 0 }}
                animate={{ opacity: 1, scaleX: 1 }}
                transition={{ duration: 0.8, delay: 1.3 }}
                className="flex items-center justify-center"
              >
                <div className="h-px w-16 bg-gradient-to-r from-transparent via-white to-transparent opacity-30" />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 1.5 }}
              >
                <Link
                  href="#what-awaits"
                  className="group inline-block text-sm uppercase tracking-[0.2em] font-light relative pb-1 border-b border-transparent hover:border-current transition-all duration-500 text-white"
                  aria-label="Odkryj podróż jogową na Bali"
                >
                  Odkryj Podróż
                  <div className="absolute bottom-0 left-0 w-0 h-px bg-current transition-all duration-500 group-hover:w-full"></div>
                </Link>
              </motion.div>
            </div>

            {/* Mobile swipe hint */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2 }}
              className="swipe-hint md:hidden text-white"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TranscendentHero;
