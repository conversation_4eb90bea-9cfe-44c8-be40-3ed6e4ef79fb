import Image from 'next/image';

const ParallaxHero = ({
  children,
  imageUrl = '/images/background/bali-hero.webp',
  className = '',
  parallaxSpeed = 0.3
}) => {
  return (
    <section className={`relative w-full h-screen flex items-center justify-center overflow-hidden ${className}`}>
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={imageUrl}
          alt="Hero Background"
          fill
          priority
          className="object-cover"
          sizes="100vw"
          quality={90}
        />
        <div className="absolute inset-0 bg-black/5" />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-8 sm:px-12 lg:px-16 text-center">
        {children}
      </div>
    </section>
  );
};

export default ParallaxHero;
