/** @type {import('next').NextConfig} */

/**
 * 🚀 PROFESSIONAL NEXT.JS 15 ESM CONFIGURATION
 * Optimized for Bakasana Travel Blog - Yoga Retreats in Bali
 * 
 * ✅ ESM (ES Modules) Modern Syntax
 * ✅ TypeScript Support
 * ✅ Professional Bundle Analysis
 * ✅ Advanced Security Headers
 * ✅ Performance Optimizations
 * ✅ SEO & PWA Ready
 */

import bundleAnalyzer from '@next/bundle-analyzer';
import CompressionPlugin from 'compression-webpack-plugin';
import webpack from 'webpack';
import path from 'path';
import { fileURLToPath } from 'url';

// ES Module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Environment variables
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';
const isAnalyze = process.env.ANALYZE === 'true';

// Bundle analyzer configuration
const withBundleAnalyzer = bundleAnalyzer({
  enabled: isAnalyze,
  openAnalyzer: true,
});

// Security headers configuration
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on',
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block',
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN',
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff',
  },
  {
    key: 'Referrer-Policy',
    value: 'strict-origin-when-cross-origin',
  },
  {
    key: 'Permissions-Policy',
    value: [
      'camera=(), microphone=(), geolocation=()',
      'interest-cohort=()',
      'browsing-topics=()',
      'fullscreen=(self)',
    ].join(', '),
  },
  {
    key: 'Content-Security-Policy',
    value: [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://va.vercel-scripts.com https://vitals.vercel-insights.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://api.mapbox.com",
      "img-src 'self' data: blob: https://images.unsplash.com https://bakasana-travel.blog https://cdn.sanity.io https://api.mapbox.com",
      "connect-src 'self' https://vitals.vercel-insights.com https://analytics.google.com https://api.mapbox.com wss://api.mapbox.com",
      "font-src 'self' https://fonts.gstatic.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "upgrade-insecure-requests",
    ].join('; '),
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=31536000; includeSubDomains; preload',
  },
];

// Performance redirects
const performanceRedirects = [
  // SEO redirects
  {
    source: '/home',
    destination: '/',
    permanent: true,
  },
  {
    source: '/index',
    destination: '/',
    permanent: true,
  },
  {
    source: '/index.html',
    destination: '/',
    permanent: true,
  },
  
  // Legacy redirects
  {
    source: '/kontact',
    destination: '/kontakt',
    permanent: true,
  },
  {
    source: '/about',
    destination: '/o-mnie',
    permanent: true,
  },
  {
    source: '/contact',
    destination: '/kontakt',
    permanent: true,
  },
  {
    source: '/gallery',
    destination: '/galeria',
    permanent: true,
  },
  {
    source: '/map',
    destination: '/mapa',
    permanent: true,
  },
  {
    source: '/booking',
    destination: '/rezerwacja',
    permanent: true,
  },
  {
    source: '/programs',
    destination: '/program',
    permanent: true,
  },
  {
    source: '/online',
    destination: '/zajecia-online',
    permanent: true,
  },
  {
    source: '/privacy',
    destination: '/polityka-prywatnosci',
    permanent: true,
  },
];

// Main configuration
const nextConfig = {
  // ========================================
  // CORE CONFIGURATION
  // ========================================
  
  // React strict mode
  reactStrictMode: true,
  
  // Build output for production
  output: 'standalone',
  
  // Remove powered by header
  poweredByHeader: false,
  
  // Enable compression
  compress: true,
  
  // Enable ETags for better caching
  generateEtags: true,
  
  // Trailing slash configuration
  trailingSlash: false,
  
  // Supported file extensions
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // ========================================
  // COMPILER OPTIMIZATIONS
  // ========================================
  
  compiler: {
    // Remove console logs in production
    removeConsole: isProduction ? {
      exclude: ['error', 'warn', 'info'],
    } : false,
  },

  // ========================================
  // EXPERIMENTAL FEATURES
  // ========================================
  
  experimental: {
    // Optimize CSS loading
    optimizeCss: true,
    
    // Web vitals for performance monitoring
    webVitalsAttribution: ['CLS', 'LCP', 'FCP', 'FID', 'TTFB', 'INP'],
    
    // Server actions configuration
    serverActions: {
      bodySizeLimit: '2mb',
      allowedOrigins: [
        process.env.NEXT_PUBLIC_SITE_URL?.replace('https://', '') || 'localhost:3002',
        'bakasana-travel.blog',
      ],
    },
    
    // Optimize serverless functions
    outputFileTracingRoot: __dirname,
    
    // Enable partial prerendering when stable
    ppr: false,
  },

  // ========================================
  // IMAGE OPTIMIZATION
  // ========================================
  
  images: {
    // Modern image formats
    formats: ['image/avif', 'image/webp'],
    
    // Remote patterns for allowed image sources
    remotePatterns: [
      { 
        protocol: 'https', 
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },
      { 
        protocol: 'https', 
        hostname: 'bakasana-travel.blog',
        pathname: '/**',
      },
      { 
        protocol: 'https', 
        hostname: 'cdn.sanity.io',
        pathname: '/**',
      },
      { 
        protocol: 'https', 
        hostname: 'api.mapbox.com',
        pathname: '/**',
      },
    ],
    
    // Responsive breakpoints
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384, 512, 640, 750, 828, 1080, 1200],
    
    // Cache TTL (1 year)
    minimumCacheTTL: 31536000,
    
    // SVG support with security
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    
    // Default loader
    loader: 'default',
    unoptimized: false,
  },

  // ========================================
  // TRANSPILATION
  // ========================================
  
  transpilePackages: [
    'lucide-react',
    'react-icons',
    '@heroicons/react',
    '@headlessui/react',
  ],

  // ========================================
  // WEBPACK CONFIGURATION
  // ========================================
  
  webpack: (config, { dev, isServer }) => {
    // Path aliases for cleaner imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/lib': path.resolve(__dirname, 'src/lib'),
      '@/data': path.resolve(__dirname, 'src/data'),
      '@/hooks': path.resolve(__dirname, 'src/hooks'),
      '@/styles': path.resolve(__dirname, 'src/styles'),
    };

    // Server-side polyfills and fixes
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        buffer: false,
        util: false,
        stream: false,
        path: false,
        os: false,
        querystring: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        constants: false,
      };

      // Add polyfill for 'self' in server environment
      config.plugins.push(
        new webpack.DefinePlugin({
          'typeof self': JSON.stringify('undefined'),
          'self': 'undefined'
        })
      );
    }

    // Production optimizations
    if (!dev && !isServer) {
      // Compression plugin
      config.plugins.push(
        new CompressionPlugin({
          test: /\.(js|css|html|svg)$/,
          threshold: 8192,
          minRatio: 0.8,
        })
      );
    }

    // Advanced bundle splitting for production
    if (isProduction) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 250000,
          cacheGroups: {
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: -10,
              reuseExistingChunk: true,
            },
            
            // React framework
            react: {
              test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
              name: 'react',
              priority: 40,
              reuseExistingChunk: true,
            },
            
            // Icons libraries
            icons: {
              test: /[\\/]node_modules[\\/](lucide-react|react-icons|@heroicons)[\\/]/,
              name: 'icons',
              priority: 30,
              reuseExistingChunk: true,
            },
            
            // UI libraries
            ui: {
              test: /[\\/]node_modules[\\/](@headlessui|@radix-ui)[\\/]/,
              name: 'ui',
              priority: 25,
              reuseExistingChunk: true,
            },
            
            // Animation libraries
            animations: {
              test: /[\\/]node_modules[\\/](framer-motion)[\\/]/,
              name: 'animations',
              priority: 20,
              reuseExistingChunk: true,
            },
            
            // Map libraries
            maps: {
              test: /[\\/]node_modules[\\/](react-map-gl|mapbox-gl)[\\/]/,
              name: 'maps',
              priority: 20,
              reuseExistingChunk: true,
            },
            
            // Analytics
            analytics: {
              test: /[\\/]node_modules[\\/](@vercel\/analytics|@vercel\/speed-insights)[\\/]/,
              name: 'analytics',
              priority: 15,
              reuseExistingChunk: true,
            },
          },
        },
      };
    }

    return config;
  },

  // ========================================
  // HEADERS CONFIGURATION
  // ========================================
  
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
      {
        source: '/images/(.*)',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
      {
        source: '/manifest.json',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
      {
        source: '/sw.js',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=0, must-revalidate' },
          { key: 'Service-Worker-Allowed', value: '/' },
        ],
      },
      {
        source: '/(favicon\\.ico|apple-touch-icon\\.png|android-chrome-.*\\.png|site\\.webmanifest)',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        ],
      },
    ];
  },

  // ========================================
  // REDIRECTS CONFIGURATION
  // ========================================
  
  async redirects() {
    return performanceRedirects;
  },

  // ========================================
  // TURBOPACK CONFIGURATION
  // ========================================
  
  turbo: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // ========================================
  // EXTERNAL PACKAGES
  // ========================================
  
  serverExternalPackages: ['sharp', 'canvas', 'jsdom'],

  // ========================================
  // LOGGING CONFIGURATION
  // ========================================
  
  logging: {
    fetches: {
      fullUrl: isProduction ? false : true,
    },
  },

  // ========================================
  // PERFORMANCE OPTIMIZATIONS
  // ========================================
  
  // Disable source maps in production
  productionBrowserSourceMaps: false,
  
  // On-demand entries configuration
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },

  // ========================================
  // BUILD CONFIGURATION
  // ========================================
  
  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false,
    tsconfigPath: './tsconfig.json',
  },

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: false,
    dirs: ['src', 'pages', 'components', 'lib'],
  },

  // Environment variables
  env: {
    CUSTOM_KEY: 'bakasana-travel-blog',
    BUILD_TIME: new Date().toISOString(),
    NODE_ENV: process.env.NODE_ENV,
  },
};

// Export with bundle analyzer if enabled
export default withBundleAnalyzer(nextConfig);