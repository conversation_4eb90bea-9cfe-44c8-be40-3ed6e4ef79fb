// src/app/layout.jsx
import React from 'react';
import './globals.css';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>_<PERSON> } from 'next/font/google';
import TranscendentNavbar from '../components/Navbar/TranscendentNavbar';
import ServerFooter from '../components/Footer/ServerFooter';
import CustomCursor from '../components/CustomCursor';
import EnhancedSmoothScroll from '../components/EnhancedSmoothScroll';
import MoodSelector from '../components/MoodSelector';
import MiniMeditationTimer from '../components/MiniMeditationTimer';
import PerformanceOptimizer from '../components/PerformanceOptimizer';
import { generateMetadata as generateSEOMetadata, generateStructuredData } from './metadata';
import {
  generateOrganizationSchema,
  generateWebsiteSchema,
  generateServiceSchema,
  generateLocalBusinessSchema
} from '../lib/structuredData';
import CriticalCSS from '../components/CriticalCSS';
import ClientResourcePreloader from '../components/ClientResourcePreloader';
import AsyncCSS from '../components/AsyncCSS';
import { ClientAnalytics } from '../components/ClientAnalytics';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import SmoothScroll from '../components/SmoothScroll';
import CookieConsentBanner from '../components/CookieConsent';
import PWAInstaller from '../components/PWAInstaller';
import WebVitalsMonitor from '../components/WebVitalsMonitor';
import { AppProvider } from '../contexts/AppContext';
import { ToastProvider } from '../contexts/ToastContext';

// Wyrafinowane fonty dla eleganckiej typografii
const montserrat = Montserrat({
  subsets: ['latin'],
  variable: '--font-montserrat',
  weight: ['300', '400', '500'],
  display: 'swap'
});

const cormorant = Cormorant_Garamond({
  subsets: ['latin'],
  variable: '--font-cormorant',
  weight: ['300', '400'],
  display: 'swap'
});

// Domyślne wartości dla metadata
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://yourdomain.com';
const siteName = process.env.NEXT_PUBLIC_SITE_NAME || 'Bali Yoga Journey';
const siteDescription = process.env.NEXT_PUBLIC_SITE_DESCRIPTION || 'Odkryj magię Bali z naszymi wycieczkami i zajęciami jogi.';

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  viewportFit: 'cover',
  themeColor: '#5a5046'
};

export const metadata = generateSEOMetadata({
  title: 'Bakasana - Retreaty Jogowe Bali & Sri Lanka | Julia Jakubowicz',
  description: '⭐ Ekskluzywne retreaty jogowe na Bali i Sri Lance z doświadczoną fizjoterapeutką. ✓ Małe grupy ✓ Transformacyjne podróże ✓ Od 2500 PLN. Odkryj harmonię ciała i ducha →',
  keywords: [
    'joga Bali retreat 2025',
    'retreat jogi Sri Lanka',
    'wyjazd joga Bali Sri Lanka',
    'warsztaty jogi Ubud Sigiriya',
    'retreat jogi dla początkujących',
    'najlepszy retreat jogi opinie',
    'ile kosztuje wyjazd na jogę',
    'joga i medytacja polska instruktorka',
    'bezpieczny wyjazd joga dla kobiet',
    'ayurveda Sri Lanka joga',
    'Julia Jakubowicz fizjoterapeutka',
    'transformacyjne podróże jogowe'
  ],
});

export default function RootLayout({ children }) {
  const structuredData = generateStructuredData({
    type: 'TravelAgency',
  });

  return (
    <html lang="pl" className={`${montserrat.variable} ${cormorant.variable}`}>
      <head>
        <CriticalCSS />

        {/* PWA Meta Tags */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="application-name" content="Bali Yoga Journey" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Bali Yoga Journey" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#8B7355" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="theme-color" content="#8B7355" />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </head>
      <body className="min-h-screen flex flex-col">
        <AppProvider>
          <ToastProvider>
            <PerformanceOptimizer>
            {/* Custom cursor for desktop */}
            <CustomCursor />

            {/* Transcendent features */}
            <MoodSelector />
            <MiniMeditationTimer />

            <div className="relative">
              <TranscendentNavbar />
              <main role="main" className="relative flex-grow">
                <div className="min-h-screen">
                  {children}
                </div>
              </main>
              <ServerFooter />
            </div>
          </PerformanceOptimizer>
          </ToastProvider>
        </AppProvider>
        <AsyncCSS />
        <ClientResourcePreloader />
        <SmoothScroll />
        {process.env.NODE_ENV === 'production' ? (
          <>
            <ClientAnalytics />
            <Analytics />
            <SpeedInsights />
          </>
        ) : (
          <div style={{ display: 'none' }}>
            {/* Analytics disabled in development */}
          </div>
        )}
        <CookieConsentBanner />
        <PWAInstaller />
        <WebVitalsMonitor />
      </body>
    </html>
  );
}