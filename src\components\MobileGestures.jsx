'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, PanInfo, useAnimation } from 'framer-motion';

// Swipe Navigation Component
export const SwipeNavigation = ({ onSwipeUp, onSwipeDown, children }) => {
  const controls = useAnimation();
  const [isDragging, setIsDragging] = useState(false);

  const handleDragEnd = (event, info) => {
    setIsDragging(false);
    const { offset, velocity } = info;
    
    // Swipe up detection
    if (offset.y < -50 && velocity.y < -500) {
      onSwipeUp && onSwipeUp();
      controls.start({ y: -20, opacity: 0.8 }).then(() => {
        controls.start({ y: 0, opacity: 1 });
      });
    }
    
    // Swipe down detection
    if (offset.y > 50 && velocity.y > 500) {
      onSwipeDown && onSwipeDown();
      controls.start({ y: 20, opacity: 0.8 }).then(() => {
        controls.start({ y: 0, opacity: 1 });
      });
    }
  };

  return (
    <motion.div
      drag="y"
      dragConstraints={{ top: 0, bottom: 0 }}
      dragElastic={0.2}
      onDragStart={() => setIsDragging(true)}
      onDragEnd={handleDragEnd}
      animate={controls}
      className={`${isDragging ? 'cursor-grabbing' : 'cursor-grab'} touch-pan-y`}
    >
      {children}
      
      {/* Visual feedback for swipe */}
      {isDragging && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-temple/80 text-white px-4 py-2 rounded-full text-sm z-50"
        >
          Przeciągnij aby nawigować
        </motion.div>
      )}
    </motion.div>
  );
};

// Pinch to Zoom Gallery
export const PinchZoomGallery = ({ images, currentIndex = 0, onIndexChange }) => {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isZoomed, setIsZoomed] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let initialDistance = 0;
    let initialScale = 1;
    let lastTouchTime = 0;

    const getDistance = (touches) => {
      const dx = touches[0].clientX - touches[1].clientX;
      const dy = touches[0].clientY - touches[1].clientY;
      return Math.sqrt(dx * dx + dy * dy);
    };

    const handleTouchStart = (e) => {
      if (e.touches.length === 2) {
        initialDistance = getDistance(e.touches);
        initialScale = scale;
        e.preventDefault();
      } else if (e.touches.length === 1) {
        const currentTime = Date.now();
        if (currentTime - lastTouchTime < 300) {
          // Double tap to zoom
          if (scale === 1) {
            setScale(2);
            setIsZoomed(true);
          } else {
            setScale(1);
            setPosition({ x: 0, y: 0 });
            setIsZoomed(false);
          }
        }
        lastTouchTime = currentTime;
      }
    };

    const handleTouchMove = (e) => {
      if (e.touches.length === 2) {
        const currentDistance = getDistance(e.touches);
        const scaleChange = currentDistance / initialDistance;
        const newScale = Math.max(0.5, Math.min(4, initialScale * scaleChange));
        
        setScale(newScale);
        setIsZoomed(newScale > 1);
        e.preventDefault();
      } else if (e.touches.length === 1 && isZoomed) {
        // Pan when zoomed
        const touch = e.touches[0];
        const rect = container.getBoundingClientRect();
        const x = (touch.clientX - rect.left - rect.width / 2) * 0.5;
        const y = (touch.clientY - rect.top - rect.height / 2) * 0.5;
        setPosition({ x, y });
      }
    };

    const handleTouchEnd = () => {
      if (scale < 1) {
        setScale(1);
        setPosition({ x: 0, y: 0 });
        setIsZoomed(false);
      }
    };

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [scale, isZoomed]);

  return (
    <div ref={containerRef} className="relative w-full h-full overflow-hidden bg-black/90 rounded-xl">
      <motion.div
        animate={{
          scale,
          x: position.x,
          y: position.y,
        }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        className="w-full h-full flex items-center justify-center"
      >
        <img
          src={images[currentIndex]}
          alt={`Gallery image ${currentIndex + 1}`}
          className="max-w-full max-h-full object-contain"
          draggable={false}
        />
      </motion.div>

      {/* Zoom indicator */}
      {isZoomed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm"
        >
          {Math.round(scale * 100)}%
        </motion.div>
      )}

      {/* Navigation dots */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {images.map((_, index) => (
          <button
            key={index}
            onClick={() => onIndexChange && onIndexChange(index)}
            className={`w-2 h-2 rounded-full transition-colors duration-200 ${
              index === currentIndex ? 'bg-white' : 'bg-white/50'
            }`}
          />
        ))}
      </div>

      {/* Instructions */}
      <motion.div
        initial={{ opacity: 1 }}
        animate={{ opacity: isZoomed ? 0 : 1 }}
        className="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white/70 text-sm text-center"
      >
        Dotknij dwukrotnie aby powiększyć
      </motion.div>
    </div>
  );
};

// Long Press to Save Quote
export const LongPressSave = ({ children, quote, author, onSave }) => {
  const [isPressed, setIsPressed] = useState(false);
  const [progress, setProgress] = useState(0);
  const timeoutRef = useRef(null);
  const intervalRef = useRef(null);

  const startLongPress = () => {
    setIsPressed(true);
    setProgress(0);
    
    intervalRef.current = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          handleLongPressComplete();
          return 100;
        }
        return prev + 5;
      });
    }, 50);

    timeoutRef.current = setTimeout(() => {
      handleLongPressComplete();
    }, 2000);
  };

  const cancelLongPress = () => {
    setIsPressed(false);
    setProgress(0);
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (intervalRef.current) clearInterval(intervalRef.current);
  };

  const handleLongPressComplete = () => {
    setIsPressed(false);
    setProgress(0);
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (intervalRef.current) clearInterval(intervalRef.current);
    
    // Trigger save action
    onSave && onSave({ quote, author });
    
    // Show success feedback
    if (navigator.vibrate) {
      navigator.vibrate(100);
    }
  };

  return (
    <div
      onTouchStart={startLongPress}
      onTouchEnd={cancelLongPress}
      onTouchCancel={cancelLongPress}
      onMouseDown={startLongPress}
      onMouseUp={cancelLongPress}
      onMouseLeave={cancelLongPress}
      className="relative select-none"
    >
      {children}
      
      {/* Progress indicator */}
      {isPressed && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute inset-0 flex items-center justify-center bg-temple/80 rounded-xl"
        >
          <div className="relative w-16 h-16">
            <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
              <circle
                cx="32"
                cy="32"
                r="28"
                fill="none"
                stroke="white"
                strokeWidth="4"
                opacity="0.3"
              />
              <motion.circle
                cx="32"
                cy="32"
                r="28"
                fill="none"
                stroke="white"
                strokeWidth="4"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 28}`}
                strokeDashoffset={`${2 * Math.PI * 28 * (1 - progress / 100)}`}
                transition={{ duration: 0.1 }}
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-white text-xs">💾</span>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

// Pull to Refresh
export const PullToRefresh = ({ onRefresh, children }) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [startY, setStartY] = useState(0);

  const handleTouchStart = (e) => {
    if (window.scrollY === 0) {
      setStartY(e.touches[0].clientY);
    }
  };

  const handleTouchMove = (e) => {
    if (window.scrollY === 0 && startY > 0) {
      const currentY = e.touches[0].clientY;
      const distance = Math.max(0, currentY - startY);
      setPullDistance(Math.min(distance, 100));
    }
  };

  const handleTouchEnd = () => {
    if (pullDistance > 60 && !isRefreshing) {
      setIsRefreshing(true);
      onRefresh && onRefresh().finally(() => {
        setIsRefreshing(false);
        setPullDistance(0);
      });
    } else {
      setPullDistance(0);
    }
    setStartY(0);
  };

  return (
    <div
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      className="relative"
    >
      {/* Pull indicator */}
      <motion.div
        animate={{ height: pullDistance }}
        className="overflow-hidden bg-temple/10 flex items-center justify-center"
      >
        <motion.div
          animate={{ 
            rotate: isRefreshing ? 360 : pullDistance * 3.6,
            scale: pullDistance > 60 ? 1.2 : 1 
          }}
          transition={{ 
            rotate: isRefreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : { duration: 0 }
          }}
          className="text-temple"
        >
          🔄
        </motion.div>
      </motion.div>
      
      {children}
    </div>
  );
};

export default {
  SwipeNavigation,
  PinchZoomGallery,
  LongPressSave,
  PullToRefresh,
};
