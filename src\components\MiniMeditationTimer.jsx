'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const MiniMeditationTimer = ({ className = '' }) => {
  const [isActive, setIsActive] = useState(false);
  const [timeLeft, setTimeLeft] = useState(180); // 3 minutes in seconds
  const [isCompleted, setIsCompleted] = useState(false);
  const [currentPhase, setCurrentPhase] = useState('prepare'); // prepare, breathe, complete
  const [breathPhase, setBreathPhase] = useState('inhale'); // inhale, hold, exhale, pause
  const [isVisible, setIsVisible] = useState(false);
  const [audioContext, setAudioContext] = useState(null);
  
  const intervalRef = useRef(null);
  const breathIntervalRef = useRef(null);

  // Breathing pattern: 4-4-4-4 (inhale-hold-exhale-pause)
  const breathingPattern = {
    inhale: 4000,
    hold: 4000,
    exhale: 4000,
    pause: 4000
  };

  const breathingInstructions = {
    prepare: 'Przygotuj się do medytacji',
    inhale: 'Wdech...',
    hold: 'Zatrzymaj oddech...',
    exhale: 'Wydech...',
    pause: 'Pauza...'
  };

  useEffect(() => {
    // Show timer after delay
    const timer = setTimeout(() => setIsVisible(true), 5000);
    
    // Initialize audio context
    if (typeof window !== 'undefined') {
      try {
        const ctx = new (window.AudioContext || window.webkitAudioContext)();
        setAudioContext(ctx);
      } catch (error) {
        console.log('Audio not supported');
      }
    }
    
    return () => clearTimeout(timer);
  }, []);

  // Play meditation bell sound
  const playBell = (frequency = 528, duration = 2) => {
    if (!audioContext) return;
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
    
    oscillator.start();
    oscillator.stop(audioContext.currentTime + duration);
  };

  // Start breathing cycle
  const startBreathingCycle = () => {
    const phases = ['inhale', 'hold', 'exhale', 'pause'];
    let currentIndex = 0;
    
    const cycleBreath = () => {
      setBreathPhase(phases[currentIndex]);
      currentIndex = (currentIndex + 1) % phases.length;
    };
    
    cycleBreath(); // Start immediately
    breathIntervalRef.current = setInterval(cycleBreath, 4000);
  };

  // Start meditation
  const startMeditation = () => {
    setIsActive(true);
    setCurrentPhase('breathe');
    setTimeLeft(180);
    setIsCompleted(false);
    
    playBell(528, 1); // Start bell
    startBreathingCycle();
    
    intervalRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          completeMeditation();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Complete meditation
  const completeMeditation = () => {
    setIsActive(false);
    setCurrentPhase('complete');
    setIsCompleted(true);
    
    if (intervalRef.current) clearInterval(intervalRef.current);
    if (breathIntervalRef.current) clearInterval(breathIntervalRef.current);
    
    playBell(432, 2); // Completion bell
    
    // Vibrate if supported
    if (navigator.vibrate) {
      navigator.vibrate([200, 100, 200]);
    }
  };

  // Stop meditation
  const stopMeditation = () => {
    setIsActive(false);
    setCurrentPhase('prepare');
    setTimeLeft(180);
    
    if (intervalRef.current) clearInterval(intervalRef.current);
    if (breathIntervalRef.current) clearInterval(breathIntervalRef.current);
  };

  // Format time display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate progress percentage
  const progress = ((180 - timeLeft) / 180) * 100;

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8 }}
      className={`fixed bottom-4 left-4 w-80 bg-white/90 backdrop-blur-md rounded-2xl border border-temple/20 p-6 shadow-xl z-30 ${className}`}
    >
      <div className="text-center">
        <h3 className="text-lg font-serif text-temple mb-2">
          Mini Medytacja
        </h3>
        <p className="text-sm text-temple/70 mb-6">
          Poczuj energię tego miejsca
        </p>

        {/* Breathing circle */}
        <div className="relative w-32 h-32 mx-auto mb-6">
          {/* Progress ring */}
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 128 128">
            <circle
              cx="64"
              cy="64"
              r="56"
              fill="none"
              stroke="currentColor"
              strokeWidth="4"
              className="text-temple/20"
            />
            <motion.circle
              cx="64"
              cy="64"
              r="56"
              fill="none"
              stroke="currentColor"
              strokeWidth="4"
              strokeLinecap="round"
              className="text-temple"
              strokeDasharray={`${2 * Math.PI * 56}`}
              strokeDashoffset={`${2 * Math.PI * 56 * (1 - progress / 100)}`}
              transition={{ duration: 0.5 }}
            />
          </svg>

          {/* Breathing animation */}
          <motion.div
            animate={{
              scale: isActive && breathPhase === 'inhale' ? 1.3 : 
                     isActive && breathPhase === 'exhale' ? 0.7 : 1,
            }}
            transition={{
              duration: breathPhase === 'inhale' || breathPhase === 'exhale' ? 4 : 0,
              ease: "easeInOut"
            }}
            className="absolute inset-4 rounded-full bg-gradient-to-br from-temple/20 via-golden/30 to-lotus/40 backdrop-blur-sm border border-temple/10 flex items-center justify-center"
          >
            {currentPhase === 'complete' ? (
              <span className="text-2xl">🙏</span>
            ) : (
              <span className="text-lg font-mono text-temple">
                {formatTime(timeLeft)}
              </span>
            )}
          </motion.div>
        </div>

        {/* Breathing instruction */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentPhase + breathPhase}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="mb-6"
          >
            <p className="text-temple/80 font-light">
              {currentPhase === 'complete' 
                ? 'Medytacja zakończona. Namaste 🙏' 
                : breathingInstructions[currentPhase === 'breathe' ? breathPhase : currentPhase]
              }
            </p>
          </motion.div>
        </AnimatePresence>

        {/* Controls */}
        <div className="flex gap-3 justify-center">
          {!isActive && !isCompleted && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={startMeditation}
              className="px-6 py-3 bg-temple/80 hover:bg-temple text-white rounded-full font-light tracking-wide transition-all duration-300"
            >
              Rozpocznij
            </motion.button>
          )}

          {isActive && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={stopMeditation}
              className="px-6 py-3 border border-temple/30 hover:border-temple/50 text-temple rounded-full font-light tracking-wide transition-all duration-300"
            >
              Zatrzymaj
            </motion.button>
          )}

          {isCompleted && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                setIsCompleted(false);
                setCurrentPhase('prepare');
              }}
              className="px-6 py-3 bg-temple/10 hover:bg-temple/20 text-temple rounded-full font-light tracking-wide transition-all duration-300"
            >
              Jeszcze raz
            </motion.button>
          )}

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsVisible(false)}
            className="px-4 py-3 text-temple/50 hover:text-temple/70 transition-colors duration-200"
          >
            ✕
          </motion.button>
        </div>

        {/* Completion message */}
        {isCompleted && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-3 bg-temple/5 rounded-lg border border-temple/10"
          >
            <p className="text-sm text-temple/70 italic">
              "W ciszy odnajdujemy prawdę. Niech ta chwila spokoju pozostanie z Tobą."
            </p>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default MiniMeditationTimer;
