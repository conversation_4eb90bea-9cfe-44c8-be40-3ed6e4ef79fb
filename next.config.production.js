/** @type {import('next').NextConfig} */

/**
 * PROFESSIONAL NEXT.JS 15 PRODUCTION CONFIGURATION
 * Optimized for Bakasana Travel Blog - Yoga Retreats in Bali
 * 
 * ✅ Next.js 15 Best Practices
 * ✅ Security Headers & CSP
 * ✅ Performance Optimization
 * ✅ SEO & Accessibility
 * ✅ Professional Bundle Splitting
 * ✅ Advanced Caching Strategy
 * ✅ Image Optimization
 * ✅ PWA Support
 */

const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';
const isAnalyze = process.env.ANALYZE === 'true';

// Bundle analyzer configuration
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: isAnalyze,
  openAnalyzer: true,
});

// Security headers configuration
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on',
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block',
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN',
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff',
  },
  {
    key: 'Referrer-Policy',
    value: 'strict-origin-when-cross-origin',
  },
  {
    key: 'Permissions-Policy',
    value: [
      'camera=(), microphone=(), geolocation=()',
      'interest-cohort=()',
      'browsing-topics=()',
      'fullscreen=(self)',
    ].join(', '),
  },
  {
    key: 'Content-Security-Policy',
    value: [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://va.vercel-scripts.com https://vitals.vercel-insights.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://api.mapbox.com",
      "img-src 'self' data: blob: https://images.unsplash.com https://bakasana-travel.blog https://cdn.sanity.io https://api.mapbox.com",
      "connect-src 'self' https://vitals.vercel-insights.com https://analytics.google.com https://api.mapbox.com wss://api.mapbox.com",
      "font-src 'self' https://fonts.gstatic.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "upgrade-insecure-requests",
    ].join('; '),
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=31536000; includeSubDomains; preload',
  },
];

// Cache headers configuration
const cacheHeaders = [
  {
    source: '/_next/static/(.*)',
    headers: [
      {
        key: 'Cache-Control',
        value: 'public, max-age=31536000, immutable',
      },
    ],
  },
  {
    source: '/images/(.*)',
    headers: [
      {
        key: 'Cache-Control',
        value: 'public, max-age=31536000, immutable',
      },
    ],
  },
  {
    source: '/(favicon\\.ico|apple-touch-icon\\.png|android-chrome-.*\\.png|site\\.webmanifest)',
    headers: [
      {
        key: 'Cache-Control',
        value: 'public, max-age=31536000, immutable',
      },
    ],
  },
  {
    source: '/manifest.json',
    headers: [
      {
        key: 'Cache-Control',
        value: 'public, max-age=31536000, immutable',
      },
    ],
  },
  {
    source: '/sw.js',
    headers: [
      {
        key: 'Cache-Control',
        value: 'public, max-age=0, must-revalidate',
      },
      {
        key: 'Service-Worker-Allowed',
        value: '/',
      },
    ],
  },
];

// Performance redirects
const performanceRedirects = [
  // SEO redirects
  {
    source: '/home',
    destination: '/',
    permanent: true,
  },
  {
    source: '/index',
    destination: '/',
    permanent: true,
  },
  {
    source: '/index.html',
    destination: '/',
    permanent: true,
  },
  
  // Internationalization redirects
  {
    source: '/about',
    destination: '/o-mnie',
    permanent: true,
  },
  {
    source: '/contact',
    destination: '/kontakt',
    permanent: true,
  },
  {
    source: '/gallery',
    destination: '/galeria',
    permanent: true,
  },
  {
    source: '/map',
    destination: '/mapa',
    permanent: true,
  },
  {
    source: '/booking',
    destination: '/rezerwacja',
    permanent: true,
  },
  {
    source: '/programs',
    destination: '/program',
    permanent: true,
  },
  {
    source: '/online',
    destination: '/zajecia-online',
    permanent: true,
  },
  {
    source: '/privacy',
    destination: '/polityka-prywatnosci',
    permanent: true,
  },
];

// Main configuration
const nextConfig = {
  // ========================================
  // CORE CONFIGURATION
  // ========================================
  
  // React strict mode for development
  reactStrictMode: true,
  
  // Build output for production
  output: 'standalone',
  
  // Remove powered by header
  poweredByHeader: false,
  
  // Enable compression
  compress: true,
  
  // Enable ETags for better caching
  generateEtags: true,
  
  // Trailing slash configuration
  trailingSlash: false,
  
  // Supported file extensions
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // ========================================
  // COMPILER OPTIMIZATIONS
  // ========================================
  
  compiler: {
    // Remove console logs in production
    removeConsole: isProduction ? {
      exclude: ['error', 'warn', 'info'],
    } : false,
    
    // Enable SWC minification
    swcMinify: true,
  },

  // ========================================
  // EXPERIMENTAL FEATURES
  // ========================================
  
  experimental: {
    // Optimize CSS loading
    optimizeCss: true,
    
    // Web vitals for performance monitoring
    webVitalsAttribution: ['CLS', 'LCP', 'FCP', 'FID', 'TTFB', 'INP'],
    
    // Server actions configuration
    serverActions: {
      bodySizeLimit: '2mb',
      allowedOrigins: ['bakasana-travel.blog', 'localhost:3002'],
    },
    
    // Optimize serverless functions
    outputFileTracingRoot: __dirname,
    
    // Enable partial prerendering when stable
    ppr: false,
  },

  // ========================================
  // IMAGE OPTIMIZATION
  // ========================================
  
  images: {
    // Modern image formats
    formats: ['image/avif', 'image/webp'],
    
    // Responsive breakpoints
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384, 512, 640, 750, 828, 1080, 1200],
    
    // Cache TTL (1 year)
    minimumCacheTTL: 31536000,
    
    // SVG support with security
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    
    // Allowed image sources
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'bakasana-travel.blog',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        pathname: '/**',
      },
    ],
  },

  // ========================================
  // WEBPACK CONFIGURATION
  // ========================================
  
  webpack: (config, { dev, isServer, webpack }) => {
    // Path aliases for cleaner imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src'),
      '@/components': require('path').resolve(__dirname, 'src/components'),
      '@/lib': require('path').resolve(__dirname, 'src/lib'),
      '@/data': require('path').resolve(__dirname, 'src/data'),
      '@/hooks': require('path').resolve(__dirname, 'src/hooks'),
      '@/styles': require('path').resolve(__dirname, 'src/styles'),
    };

    // Client-side polyfills
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        buffer: false,
        util: false,
        stream: false,
        path: false,
        os: false,
        querystring: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        constants: false,
        _stream_duplex: false,
        _stream_passthrough: false,
        _stream_readable: false,
        _stream_writable: false,
        _stream_transform: false,
      };
    }

    // Production optimizations
    if (isProduction) {
      // Advanced bundle splitting
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000,
        maxSize: 250000,
        minRemainingSize: 0,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        enforceSizeThreshold: 50000,
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            reuseExistingChunk: true,
          },
          
          // Framework chunks
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            priority: 40,
            reuseExistingChunk: true,
          },
          
          // Animation libraries
          animations: {
            test: /[\\/]node_modules[\\/](framer-motion|@headlessui)[\\/]/,
            name: 'animations',
            priority: 30,
            reuseExistingChunk: true,
          },
          
          // UI components
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|lucide-react|@heroicons)[\\/]/,
            name: 'ui',
            priority: 25,
            reuseExistingChunk: true,
          },
          
          // Map libraries
          maps: {
            test: /[\\/]node_modules[\\/](react-map-gl|mapbox-gl)[\\/]/,
            name: 'maps',
            priority: 20,
            reuseExistingChunk: true,
          },
          
          // Calendar libraries
          calendar: {
            test: /[\\/]node_modules[\\/](react-big-calendar|moment)[\\/]/,
            name: 'calendar',
            priority: 20,
            reuseExistingChunk: true,
          },
          
          // Analytics
          analytics: {
            test: /[\\/]node_modules[\\/](@vercel\/analytics|@vercel\/speed-insights)[\\/]/,
            name: 'analytics',
            priority: 15,
            reuseExistingChunk: true,
          },
          
          // Common chunks
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      };

      // Tree shaking optimization
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
      config.optimization.providedExports = true;
      config.optimization.innerGraph = true;
      config.optimization.realContentHash = true;
    }

    // SVG handling with optimization
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: '@svgr/webpack',
          options: {
            svgoConfig: {
              plugins: [
                {
                  name: 'removeViewBox',
                  active: false,
                },
                {
                  name: 'addAttributesToSVGElement',
                  params: {
                    attributes: [{ 'aria-hidden': 'true' }],
                  },
                },
                {
                  name: 'removeDimensions',
                  active: true,
                },
              ],
            },
          },
        },
      ],
    });

    // Compression plugins for production
    if (isProduction) {
      const CompressionPlugin = require('compression-webpack-plugin');
      
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css|html|svg)$/,
          threshold: 8192,
          minRatio: 0.8,
        }),
      );
    }

    return config;
  },

  // ========================================
  // HEADERS CONFIGURATION
  // ========================================
  
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
      ...cacheHeaders,
    ];
  },

  // ========================================
  // REDIRECTS CONFIGURATION
  // ========================================
  
  async redirects() {
    return performanceRedirects;
  },

  // ========================================
  // DEVELOPMENT CONFIGURATION
  // ========================================
  
  ...(isDevelopment && {
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: '/api/:path*',
        },
      ];
    },
  }),

  // ========================================
  // BUILD CONFIGURATION
  // ========================================
  
  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false,
    tsconfigPath: './tsconfig.json',
  },

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: false,
    dirs: ['src', 'pages', 'components', 'lib'],
  },

  // Logging configuration
  logging: {
    fetches: {
      fullUrl: true,
    },
  },

  // Environment variables
  env: {
    CUSTOM_KEY: 'bakasana-travel-blog',
    BUILD_TIME: new Date().toISOString(),
    NODE_ENV: process.env.NODE_ENV,
  },

  // External packages for server components
  serverExternalPackages: ['sharp', 'canvas', 'jsdom'],
};

// Export configuration with bundle analyzer if enabled
module.exports = isAnalyze ? withBundleAnalyzer(nextConfig) : nextConfig;