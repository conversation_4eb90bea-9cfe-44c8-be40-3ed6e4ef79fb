/** @type {import('next').NextConfig} */

/**
 * Professional Next.js 15 Configuration for Production
 * Optimized for Bakasana Travel Blog - Yoga Retreats in Bali
 * 
 * Features:
 * - ✅ Next.js 15 stable features
 * - ✅ Advanced performance optimization
 * - ✅ Enhanced security headers
 * - ✅ SEO optimization
 * - ✅ Image optimization
 * - ✅ Bundle optimization
 * - ✅ Professional caching strategy
 */

const isProduction = process.env.NODE_ENV === 'production';
const isAnalyze = process.env.ANALYZE === 'true';

// Bundle analyzer for development insights
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: isAnalyze,
  openAnalyzer: true,
});

const nextConfig = {
  // ========================================
  // CORE CONFIGURATION
  // ========================================
  
  // Build output optimization
  output: 'standalone',
  
  // Compiler optimizations
  compiler: {
    // Remove console logs in production (keep errors and warnings)
    removeConsole: isProduction ? {
      exclude: ['error', 'warn', 'info'],
    } : false,
  },

  // Enable React strict mode
  reactStrictMode: true,

  // ========================================
  // PERFORMANCE OPTIMIZATIONS
  // ========================================
  



  // Experimental features
  experimental: {
    // Enable optimized CSS loading
    optimizeCss: true,
    
    // Enable web vitals attribution for better debugging
    webVitalsAttribution: ['CLS', 'LCP', 'FCP', 'FID', 'TTFB', 'INP'],
    
    // Enable server actions for better form handling
    serverActions: {
      bodySizeLimit: '2mb',
    },
    
    // Enable partial prerendering for better performance
    ppr: false, // Enable when stable
  },

  // External packages for server components
  serverExternalPackages: ['sharp', 'canvas'],

  // ========================================
  // IMAGE OPTIMIZATION
  // ========================================
  
  images: {
    // Supported formats (modern first)
    formats: ['image/avif', 'image/webp'],
    
    // Responsive breakpoints
    deviceSizes: [480, 640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384, 512, 640, 750, 828, 1080, 1200],
    
    // Cache optimization (1 year)
    minimumCacheTTL: 31536000,
    
    // SVG support with security
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    
    // Allowed image sources
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'bakasana-travel.blog',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        pathname: '/**',
      },
    ],
    
    // Disable image optimization for static export if needed
    unoptimized: false,
  },

  // ========================================
  // WEBPACK CONFIGURATION
  // ========================================
  
  webpack: (config, { dev, isServer, webpack }) => {
    // Path aliases for cleaner imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src'),
      '@/components': require('path').resolve(__dirname, 'src/components'),
      '@/lib': require('path').resolve(__dirname, 'src/lib'),
      '@/data': require('path').resolve(__dirname, 'src/data'),
      '@/hooks': require('path').resolve(__dirname, 'src/hooks'),
    };

    // Client-side polyfills
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        buffer: false,
        util: false,
        stream: false,
        path: false,
        os: false,
        querystring: false,
        url: false,
      };
    }

    // Production optimizations
    if (isProduction) {
      // Advanced bundle splitting
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000,
        maxSize: 250000,
        cacheGroups: {
          // Vendor libraries
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: 10,
            reuseExistingChunk: true,
          },
          
          // Animation libraries
          animations: {
            test: /[\\/]node_modules[\\/](framer-motion|@headlessui)[\\/]/,
            name: 'animations',
            priority: 30,
            reuseExistingChunk: true,
          },
          
          // UI components
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|lucide-react|@heroicons)[\\/]/,
            name: 'ui',
            priority: 25,
            reuseExistingChunk: true,
          },
          
          // Map libraries
          maps: {
            test: /[\\/]node_modules[\\/](react-map-gl|mapbox-gl)[\\/]/,
            name: 'maps',
            priority: 20,
            reuseExistingChunk: true,
          },
          
          // Calendar libraries
          calendar: {
            test: /[\\/]node_modules[\\/](react-big-calendar|moment)[\\/]/,
            name: 'calendar',
            priority: 20,
            reuseExistingChunk: true,
          },
          
          // Analytics
          analytics: {
            test: /[\\/]node_modules[\\/](@vercel\/analytics|@vercel\/speed-insights)[\\/]/,
            name: 'analytics',
            priority: 15,
            reuseExistingChunk: true,
          },
          
          // Common chunks
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      };

      // Tree shaking
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
      config.optimization.providedExports = true;
    }

    // SVG handling
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: '@svgr/webpack',
          options: {
            svgoConfig: {
              plugins: [
                {
                  name: 'removeViewBox',
                  active: false,
                },
                {
                  name: 'addAttributesToSVGElement',
                  params: {
                    attributes: [{ 'aria-hidden': 'true' }],
                  },
                },
              ],
            },
          },
        },
      ],
    });

    // Compression for production
    if (isProduction) {
      const CompressionPlugin = require('compression-webpack-plugin');
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css|html|svg)$/,
          threshold: 8192,
          minRatio: 0.8,
        }),
      );
    }

    return config;
  },

  // ========================================
  // SECURITY HEADERS
  // ========================================
  
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // DNS prefetch control
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          
          // XSS protection
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          
          // Frame options
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          
          // Content type options
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          
          // Referrer policy
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          
          // Permissions policy
          {
            key: 'Permissions-Policy',
            value: [
              'camera=(), microphone=(), geolocation=()',
              'interest-cohort=()',
              'browsing-topics=()',
            ].join(', '),
          },
          
          // Content Security Policy
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://va.vercel-scripts.com https://vitals.vercel-insights.com",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://api.mapbox.com",
              "img-src 'self' data: blob: https://images.unsplash.com https://bakasana-travel.blog https://cdn.sanity.io https://api.mapbox.com",
              "connect-src 'self' https://vitals.vercel-insights.com https://analytics.google.com https://api.mapbox.com wss://api.mapbox.com",
              "font-src 'self' https://fonts.gstatic.com",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests",
            ].join('; '),
          },
          
          // Strict Transport Security
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload',
          },
        ],
      },
      
      // Static assets caching
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          },
          {
            key: 'Service-Worker-Allowed',
            value: '/',
          },
        ],
      },
      
      // App manifest
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      
      // Next.js static files
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      
      // Images
      {
        source: '/images/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      
      // Icons and favicons
      {
        source: '/(favicon\\.ico|apple-touch-icon\\.png|android-chrome-.*\\.png|site\\.webmanifest)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // ========================================
  // REDIRECTS & REWRITES
  // ========================================
  
  async redirects() {
    return [
      // SEO redirects
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/index',
        destination: '/',
        permanent: true,
      },
      {
        source: '/index.html',
        destination: '/',
        permanent: true,
      },
      
      // Legacy redirects
      {
        source: '/about',
        destination: '/o-mnie',
        permanent: true,
      },
      {
        source: '/contact',
        destination: '/kontakt',
        permanent: true,
      },
      {
        source: '/gallery',
        destination: '/galeria',
        permanent: true,
      },
      {
        source: '/map',
        destination: '/mapa',
        permanent: true,
      },
      {
        source: '/booking',
        destination: '/rezerwacja',
        permanent: true,
      },
      {
        source: '/programs',
        destination: '/program',
        permanent: true,
      },
      {
        source: '/online',
        destination: '/zajecia-online',
        permanent: true,
      },
      {
        source: '/privacy',
        destination: '/polityka-prywatnosci',
        permanent: true,
      },
    ];
  },

  // ========================================
  // DEVELOPMENT CONFIGURATION
  // ========================================
  
  // Development server configuration
  ...(process.env.NODE_ENV === 'development' && {
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: '/api/:path*',
        },
      ];
    },
  }),

  // ========================================
  // BUILD CONFIGURATION
  // ========================================
  
  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false,
    tsconfigPath: './tsconfig.json',
  },

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: false,
    dirs: ['src', 'pages', 'components', 'lib'],
  },

  // Logging configuration
  logging: {
    fetches: {
      fullUrl: true,
    },
  },

  // Environment variables
  env: {
    CUSTOM_KEY: 'bakasana-travel-blog',
    BUILD_TIME: new Date().toISOString(),
  },

  // ========================================
  // PERFORMANCE MONITORING
  // ========================================
  
  // Disable powered by header
  poweredByHeader: false,
  
  // Enable compression
  compress: true,
  
  // Enable ETags
  generateEtags: true,
  
  // Page extensions
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],
  
  // Trailing slash handling
  trailingSlash: false,
};

// Export with bundle analyzer if enabled
module.exports = isAnalyze ? withBundleAnalyzer(nextConfig) : nextConfig;
