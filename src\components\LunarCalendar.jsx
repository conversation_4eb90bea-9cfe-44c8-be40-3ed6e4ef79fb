'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const LunarCalendar = ({ className = '' }) => {
  const [currentPhase, setCurrentPhase] = useState(null);
  const [upcomingRetreatDates, setUpcomingRetreatDates] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  // Lunar phases data (simplified calculation)
  const lunarPhases = [
    { name: 'N<PERSON>', emoji: '🌑', energy: 'new beginnings', multiplier: 1.0 },
    { name: 'P<PERSON>ybywający sierp', emoji: '🌒', energy: 'growth', multiplier: 1.1 },
    { name: '<PERSON><PERSON><PERSON> kwadra', emoji: '🌓', energy: 'action', multiplier: 1.2 },
    { name: 'Przybywający garb', emoji: '🌔', energy: 'refinement', multiplier: 1.3 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', emoji: '🌕', energy: 'culmination', multiplier: 1.5 },
    { name: 'Ubywający garb', emoji: '🌖', energy: 'gratitude', multiplier: 1.3 },
    { name: 'Ostatnia kwadra', emoji: '🌗', energy: 'release', multiplier: 1.2 },
    { name: 'Ubywający sierp', emoji: '🌘', energy: 'reflection', multiplier: 1.1 }
  ];

  // Mock retreat dates with lunar alignment
  const retreatDates = [
    {
      id: 'bali-march-2025',
      destination: 'Bali',
      startDate: '2025-03-15',
      endDate: '2025-03-27',
      lunarPhase: 'Pełnia',
      basePrice: 3200,
      description: 'Retreat podczas pełni księżyca - czas kulminacji i transformacji'
    },
    {
      id: 'srilanka-april-2025',
      destination: 'Sri Lanka',
      startDate: '2025-04-12',
      endDate: '2025-04-22',
      lunarPhase: 'Nów',
      basePrice: 2900,
      description: 'Retreat podczas nowiu - czas nowych początków'
    },
    {
      id: 'bali-may-2025',
      destination: 'Bali',
      startDate: '2025-05-18',
      endDate: '2025-05-30',
      lunarPhase: 'Pierwsza kwadra',
      basePrice: 3400,
      description: 'Retreat podczas pierwszej kwadry - czas działania i manifestacji'
    },
    {
      id: 'srilanka-june-2025',
      destination: 'Sri Lanka',
      startDate: '2025-06-14',
      endDate: '2025-06-24',
      lunarPhase: 'Pełnia',
      basePrice: 3100,
      description: 'Retreat podczas pełni księżyca - maksymalna energia duchowa'
    }
  ];

  // Calculate current lunar phase (simplified)
  const calculateLunarPhase = () => {
    const now = new Date();
    const lunarCycle = 29.53; // days
    const knownNewMoon = new Date('2024-01-11'); // Known new moon date
    const daysSinceNewMoon = (now - knownNewMoon) / (1000 * 60 * 60 * 24);
    const currentCycle = daysSinceNewMoon % lunarCycle;
    const phaseIndex = Math.floor((currentCycle / lunarCycle) * 8);
    
    return lunarPhases[phaseIndex] || lunarPhases[0];
  };

  // Calculate dynamic pricing based on lunar phase
  const calculateLunarPrice = (basePrice, lunarPhase) => {
    const phase = lunarPhases.find(p => p.name === lunarPhase);
    return Math.round(basePrice * (phase?.multiplier || 1.0));
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Check if date is optimal for spiritual practice
  const isOptimalDate = (lunarPhase) => {
    return ['Pełnia', 'Nów', 'Pierwsza kwadra'].includes(lunarPhase);
  };

  useEffect(() => {
    setCurrentPhase(calculateLunarPhase());
    setUpcomingRetreatDates(retreatDates);
    
    // Show component after delay
    const timer = setTimeout(() => setIsVisible(true), 3000);
    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 1 }}
      className={`bg-gradient-to-br from-temple/5 to-bamboo/5 rounded-2xl p-8 border border-temple/10 ${className}`}
    >
      <div className="text-center mb-8">
        <h3 className="text-2xl font-serif text-temple mb-4">
          Kalendarz Księżycowy
        </h3>
        <p className="text-temple/70 mb-6">
          Nasze retreaty są synchronizowane z cyklami księżyca dla maksymalnej transformacji duchowej
        </p>
        
        {/* Current lunar phase */}
        {currentPhase && (
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 4, repeat: Infinity }}
            className="inline-flex items-center gap-3 bg-white/50 backdrop-blur-sm rounded-full px-6 py-3 border border-temple/20"
          >
            <span className="text-2xl">{currentPhase.emoji}</span>
            <div className="text-left">
              <div className="font-medium text-temple">{currentPhase.name}</div>
              <div className="text-sm text-temple/60 capitalize">{currentPhase.energy}</div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Upcoming retreats */}
      <div className="space-y-4">
        <h4 className="text-lg font-serif text-temple text-center mb-6">
          Nadchodzące Retreaty
        </h4>
        
        {upcomingRetreatDates.map((retreat, index) => {
          const lunarPrice = calculateLunarPrice(retreat.basePrice, retreat.lunarPhase);
          const isOptimal = isOptimalDate(retreat.lunarPhase);
          const phase = lunarPhases.find(p => p.name === retreat.lunarPhase);
          
          return (
            <motion.div
              key={retreat.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.2 }}
              className={`p-6 bg-white/40 backdrop-blur-sm rounded-xl border transition-all duration-300 hover:shadow-lg ${
                isOptimal 
                  ? 'border-golden/30 hover:border-golden/50 bg-gradient-to-r from-golden/5 to-temple/5' 
                  : 'border-temple/10 hover:border-temple/20'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h5 className="font-serif text-lg text-temple mb-1">
                    {retreat.destination}
                  </h5>
                  <p className="text-sm text-temple/70">
                    {formatDate(retreat.startDate)} - {formatDate(retreat.endDate)}
                  </p>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-xl">{phase?.emoji}</span>
                    <span className="text-sm font-medium text-temple">
                      {retreat.lunarPhase}
                    </span>
                  </div>
                  {isOptimal && (
                    <span className="text-xs bg-golden/20 text-golden-dark px-2 py-1 rounded-full">
                      Optymalna energia
                    </span>
                  )}
                </div>
              </div>
              
              <p className="text-sm text-temple/80 mb-4 leading-relaxed">
                {retreat.description}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="text-temple">
                  <span className="text-lg font-semibold">
                    {lunarPrice.toLocaleString('pl-PL')} PLN
                  </span>
                  {lunarPrice !== retreat.basePrice && (
                    <span className="text-sm text-temple/60 line-through ml-2">
                      {retreat.basePrice.toLocaleString('pl-PL')} PLN
                    </span>
                  )}
                </div>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-2 bg-temple/10 hover:bg-temple/20 text-temple rounded-lg text-sm font-medium transition-colors duration-200"
                >
                  Dowiedz się więcej
                </motion.button>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Lunar wisdom */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
        className="mt-8 p-4 bg-temple/5 rounded-xl border border-temple/10"
      >
        <p className="text-sm text-temple/70 text-center italic leading-relaxed">
          "Księżyc wpływa na nasze emocje i energię duchową. Retreaty podczas pełni księżyca 
          intensyfikują transformację, podczas gdy nów sprzyja nowym początkom."
        </p>
      </motion.div>
    </motion.div>
  );
};

export default LunarCalendar;
