'use client';

import React, { useState, useEffect } from 'react';
import { motion, useMotionValue, useSpring } from 'framer-motion';

const CustomCursor = () => {
  const [isHovering, setIsHovering] = useState(false);
  const [cursorType, setCursorType] = useState('default'); // 'default', 'link', 'button'
  const [isVisible, setIsVisible] = useState(false);

  const cursorX = useMotionValue(-100);
  const cursorY = useMotionValue(-100);

  const springConfig = { damping: 25, stiffness: 700 };
  const cursorXSpring = useSpring(cursorX, springConfig);
  const cursorYSpring = useSpring(cursorY, springConfig);

  useEffect(() => {
    const moveCursor = (e) => {
      cursorX.set(e.clientX - 16);
      cursorY.set(e.clientY - 16);
      setIsVisible(true);
    };

    const handleMouseEnter = () => setIsVisible(true);
    const handleMouseLeave = () => setIsVisible(false);

    // Handle hover states
    const handleElementHover = (e) => {
      const target = e.target;
      
      if (target.tagName === 'A' || target.closest('a')) {
        setCursorType('link');
        setIsHovering(true);
      } else if (target.tagName === 'BUTTON' || target.closest('button')) {
        setCursorType('button');
        setIsHovering(true);
      } else {
        setCursorType('default');
        setIsHovering(false);
      }
    };

    const handleElementLeave = () => {
      setCursorType('default');
      setIsHovering(false);
    };

    // Add event listeners
    document.addEventListener('mousemove', moveCursor);
    document.addEventListener('mouseenter', handleMouseEnter);
    document.addEventListener('mouseleave', handleMouseLeave);
    
    // Add hover listeners to all interactive elements
    const interactiveElements = document.querySelectorAll('a, button, [role="button"]');
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleElementHover);
      el.addEventListener('mouseleave', handleElementLeave);
    });

    return () => {
      document.removeEventListener('mousemove', moveCursor);
      document.removeEventListener('mouseenter', handleMouseEnter);
      document.removeEventListener('mouseleave', handleMouseLeave);
      
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleElementHover);
        el.removeEventListener('mouseleave', handleElementLeave);
      });
    };
  }, [cursorX, cursorY]);

  // Hide default cursor
  useEffect(() => {
    document.body.style.cursor = 'none';
    return () => {
      document.body.style.cursor = 'auto';
    };
  }, []);

  if (!isVisible) return null;

  return (
    <>
      {/* Main cursor */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-50 mix-blend-difference"
        style={{
          x: cursorXSpring,
          y: cursorYSpring,
        }}
      >
        <motion.div
          animate={{
            scale: isHovering ? 1.5 : 1,
            opacity: isVisible ? 1 : 0,
          }}
          transition={{
            type: "spring",
            stiffness: 500,
            damping: 28,
          }}
          className="relative"
        >
          {/* Breathing circle */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.6, 1, 0.6],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className="w-8 h-8 rounded-full border border-temple/60 bg-temple/10 backdrop-blur-sm"
          />

          {/* Inner dot */}
          <motion.div
            animate={{
              scale: isHovering ? 0.5 : 1,
            }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-temple"
          />

          {/* Lotus transformation on link hover */}
          {cursorType === 'link' && (
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                className="text-temple"
              >
                <path
                  d="M12 2C12 2 8 6 8 10C8 12 10 14 12 14C14 14 16 12 16 10C16 6 12 2 12 2Z"
                  fill="currentColor"
                  opacity="0.6"
                />
                <path
                  d="M12 22C12 22 16 18 16 14C16 12 14 10 12 10C10 10 8 12 8 14C8 18 12 22 12 22Z"
                  fill="currentColor"
                  opacity="0.6"
                />
                <path
                  d="M2 12C2 12 6 8 10 8C12 8 14 10 14 12C14 14 12 16 10 16C6 16 2 12 2 12Z"
                  fill="currentColor"
                  opacity="0.6"
                />
                <path
                  d="M22 12C22 12 18 16 14 16C12 16 10 14 10 12C10 10 12 8 14 8C18 8 22 12 22 12Z"
                  fill="currentColor"
                  opacity="0.6"
                />
              </svg>
            </motion.div>
          )}

          {/* Ripple effect for buttons */}
          {cursorType === 'button' && (
            <motion.div
              animate={{
                scale: [1, 2, 1],
                opacity: [0.3, 0.1, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeOut",
              }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 rounded-full border border-temple/30"
            />
          )}
        </motion.div>
      </motion.div>

      {/* Incense trail effect */}
      <motion.div
        className="fixed top-0 left-0 pointer-events-none z-40"
        style={{
          x: cursorXSpring,
          y: cursorYSpring,
        }}
      >
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 0.3, 0],
              y: [0, -20, -40],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeOut",
            }}
            className="absolute w-1 h-1 bg-temple/20 rounded-full"
            style={{
              left: `${-2 + Math.random() * 4}px`,
              top: `${-2 + Math.random() * 4}px`,
            }}
          />
        ))}
      </motion.div>
    </>
  );
};

export default CustomCursor;
