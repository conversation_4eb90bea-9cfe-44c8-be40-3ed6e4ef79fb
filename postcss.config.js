/**
 * 🎨 PROFESSIONAL POSTCSS CONFIGURATION
 * Optimized for Next.js 15 & Tailwind CSS
 * 
 * ✅ Tailwind CSS with Nesting
 * ✅ Autoprefixer for Browser Compatibility
 * ✅ CSS Optimization for Production
 * ✅ Professional Minification
 */

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  plugins: {
    // Tailwind CSS with nesting support
    'tailwindcss/nesting': {},
    tailwindcss: {},
    
    // Autoprefixer for browser compatibility
    autoprefixer: {
      flexbox: 'no-2009',
      grid: 'autoplace',
    },
    
    // Production optimizations
    ...(isProduction && {
      // Professional CSS minification
      cssnano: {
        preset: ['default', {
          discardComments: {
            removeAll: true,
          },
          discardDuplicates: true,
          discardEmpty: true,
          discardOverridden: true,
          mergeIdents: true,
          mergeLonghand: true,
          mergeRules: true,
          minifyFontValues: true,
          minifyGradients: true,
          minifyParams: true,
          minifySelectors: true,
          normalizeCharset: true,
          normalizeDisplayValues: true,
          normalizePositions: true,
          normalizeRepeatStyle: true,
          normalizeString: true,
          normalizeTimingFunctions: true,
          normalizeUnicode: true,
          normalizeUrl: true,
          normalizeWhitespace: true,
          orderedValues: true,
          reduceIdents: true,
          reduceInitial: true,
          reduceTransforms: true,
          svgo: true,
          uniqueSelectors: true,
          zindex: false, // Keep z-index values as-is for better compatibility
        }],
      },
    }),
  },
};