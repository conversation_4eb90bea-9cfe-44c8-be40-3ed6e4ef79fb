'use client';

import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const VideoHero = ({ 
  videoSrc = '/videos/bali-hero.mp4', 
  fallbackImage = '/images/background/bali-hero.webp',
  children,
  overlayOpacity = 0.4 
}) => {
  const videoRef = useRef(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedData = () => {
      setIsVideoLoaded(true);
      // Slow down video to 0.7x speed for more meditative feel
      video.playbackRate = 0.7;
    };

    const handleError = () => {
      setHasError(true);
      console.log('Video failed to load, using fallback image');
    };

    const handleCanPlay = () => {
      video.play().catch(() => {
        // Autoplay failed, that's okay
        setHasError(true);
      });
    };

    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('error', handleError);
    video.addEventListener('canplay', handleCanPlay);

    return () => {
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('error', handleError);
      video.removeEventListener('canplay', handleCanPlay);
    };
  }, []);

  return (
    <section className="relative h-screen overflow-hidden">
      {/* Video Background */}
      {!hasError && (
        <motion.video
          ref={videoRef}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ 
            opacity: isVideoLoaded ? 1 : 0,
            scale: isVideoLoaded ? 1 : 1.1
          }}
          transition={{ duration: 2 }}
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
        >
          <source src={videoSrc} type="video/mp4" />
        </motion.video>
      )}

      {/* Fallback Image */}
      <motion.div
        initial={{ opacity: 1 }}
        animate={{ opacity: (isVideoLoaded && !hasError) ? 0 : 1 }}
        transition={{ duration: 2 }}
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${fallbackImage})` }}
      />

      {/* Gradient Overlay */}
      <div 
        className="absolute inset-0 bg-gradient-to-b from-temple/30 via-temple/10 to-temple/30"
        style={{ opacity: overlayOpacity }}
      />

      {/* Breathing overlay effect */}
      <motion.div
        animate={{
          opacity: [0.1, 0.3, 0.1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="absolute inset-0 bg-gradient-radial from-golden/10 via-transparent to-transparent"
      />

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -40, 0],
              opacity: [0.1, 0.4, 0.1],
              scale: [0.5, 1.2, 0.5],
            }}
            transition={{
              duration: 12 + i * 0.8,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "easeInOut",
            }}
            className="absolute w-1 h-1 bg-white/30 rounded-full"
            style={{
              left: `${5 + (i * 3.8)}%`,
              top: `${10 + (i % 6) * 15}%`,
            }}
          />
        ))}
      </div>

      {/* Sacred geometry overlay */}
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 120, repeat: Infinity, ease: "linear" }}
        className="absolute inset-0 flex items-center justify-center pointer-events-none"
      >
        <svg
          width="800"
          height="800"
          viewBox="0 0 800 800"
          className="opacity-5"
        >
          {/* Flower of Life pattern */}
          <g stroke="white" strokeWidth="1" fill="none">
            <circle cx="400" cy="400" r="60" />
            <circle cx="400" cy="340" r="60" />
            <circle cx="400" cy="460" r="60" />
            <circle cx="348" cy="370" r="60" />
            <circle cx="452" cy="370" r="60" />
            <circle cx="348" cy="430" r="60" />
            <circle cx="452" cy="430" r="60" />
          </g>
        </svg>
      </motion.div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center justify-center">
        {children}
      </div>

      {/* Scroll indicator */}
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60"
      >
        <div className="flex flex-col items-center space-y-2">
          <span className="text-xs uppercase tracking-wider">Przewiń w dół</span>
          <svg className="w-4 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </motion.div>

      {/* Video controls (hidden but accessible) */}
      {!hasError && (
        <div className="absolute bottom-4 right-4 opacity-0 hover:opacity-100 transition-opacity duration-300">
          <button
            onClick={() => {
              const video = videoRef.current;
              if (video) {
                if (video.paused) {
                  video.play();
                } else {
                  video.pause();
                }
              }
            }}
            className="p-2 bg-black/30 rounded-full text-white/70 hover:text-white transition-colors duration-200"
            aria-label="Toggle video playback"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        </div>
      )}
    </section>
  );
};

export default VideoHero;
