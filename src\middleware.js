/**
 * 🛡️ PROFESSIONAL MIDDLEWARE CONFIGURATION
 * Enterprise-grade security and performance optimization
 * 
 * ✅ JWT Authentication
 * ✅ Advanced CORS Configuration
 * ✅ Security Headers
 * ✅ Rate Limiting
 * ✅ Request Optimization
 * ✅ Bot Protection
 */

import { NextResponse } from 'next/server';
import { jwtVerify } from 'jose';

// Security constants
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const ALLOWED_ORIGINS = [
  'https://bakasana-travel.blog',
  'https://www.bakasana-travel.blog',
  ...(process.env.NODE_ENV === 'development' ? ['http://localhost:3002', 'http://localhost:3000'] : [])
];

// Rate limiting storage (in production, use Redis)
const rateLimitMap = new Map();

// Rate limiting function
function rateLimit(ip, limit = 100, window = 60000) {
  const now = Date.now();
  const userRequests = rateLimitMap.get(ip) || [];
  
  // Filter out old requests
  const recentRequests = userRequests.filter(timestamp => now - timestamp < window);
  
  // Check if limit exceeded
  if (recentRequests.length >= limit) {
    return false;
  }
  
  // Add current request
  recentRequests.push(now);
  rateLimitMap.set(ip, recentRequests);
  
  return true;
}

// Bot detection function
function detectBot(userAgent) {
  const botPatterns = [
    /bot/i, /crawler/i, /spider/i, /scraper/i,
    /googlebot/i, /bingbot/i, /facebookexternalhit/i,
    /twitterbot/i, /linkedinbot/i, /whatsapp/i,
    /telegram/i, /slackbot/i, /discordbot/i
  ];
  
  return botPatterns.some(pattern => pattern.test(userAgent));
}

export async function middleware(request) {
  const path = request.nextUrl.pathname;
  const origin = request.headers.get('origin');
  const userAgent = request.headers.get('user-agent') || '';
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  
  // Performance optimization - Skip middleware for static files
  if (path.startsWith('/_next/') || path.startsWith('/public/') || path.includes('.')) {
    return NextResponse.next();
  }

  // Rate limiting for API routes
  if (path.startsWith('/api/') && !detectBot(userAgent)) {
    if (!rateLimit(ip, 100, 60000)) {
      return new NextResponse('Too Many Requests', { 
        status: 429,
        headers: {
          'Retry-After': '60',
          'X-RateLimit-Limit': '100',
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': String(Date.now() + 60000)
        }
      });
    }
  }

  // Admin routes protection
  if (path.startsWith('/admin') && path !== '/admin') {
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : 
                 request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.redirect(new URL('/admin', request.url));
    }

    try {
      const secret = new TextEncoder().encode(JWT_SECRET);
      const { payload } = await jwtVerify(token, secret);
      
      if (payload.role !== 'admin') {
        return NextResponse.redirect(new URL('/admin', request.url));
      }
      
      // Check token expiration
      if (payload.exp && payload.exp < Date.now() / 1000) {
        return NextResponse.redirect(new URL('/admin', request.url));
      }
    } catch (error) {
      console.error('JWT verification failed:', error);
      return NextResponse.redirect(new URL('/admin', request.url));
    }
  }

  // Create response with security headers
  const response = NextResponse.next();

  // CORS configuration for API routes
  if (path.startsWith('/api/')) {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': ALLOWED_ORIGINS.includes(origin) ? origin : 'null',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
          'Access-Control-Max-Age': '86400',
          'Access-Control-Allow-Credentials': 'true',
          'Vary': 'Origin',
        }
      });
    }

    // Set CORS headers for actual requests
    if (ALLOWED_ORIGINS.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
      response.headers.set('Access-Control-Allow-Credentials', 'true');
    }
    
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    response.headers.set('Vary', 'Origin');
  }

  // Security headers for all responses
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'SAMEORIGIN');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-DNS-Prefetch-Control', 'on');
  
  // Additional security headers
  response.headers.set('X-Robots-Tag', 'index, follow');
  response.headers.set('X-Permitted-Cross-Domain-Policies', 'none');
  response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
  response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
  response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');

  // Performance headers
  response.headers.set('X-Request-ID', `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  
  // Cache control for static content
  if (path.startsWith('/images/') || path.startsWith('/static/')) {
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  }

  return response;
}

// Advanced matcher configuration
export const config = {
  matcher: [
    // API routes
    '/api/:path*',
    
    // Admin routes
    '/admin/:path*',
    
    // Protected pages
    '/rezerwacja/:path*',
    '/program/:path*',
    
    // All pages except static files
    '/((?!_next/static|_next/image|favicon.ico|manifest.json|sw.js|robots.txt|sitemap.xml|images|.*\\.(?:jpg|jpeg|gif|png|svg|ico|webp|avif|css|js|woff|woff2|ttf|eot)).*)',
  ],
};