'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const TranscendentNavbar = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeItem, setActiveItem] = useState('');
  const pathname = usePathname();

  const navigationLinks = [
    { href: '/', label: 'Strona główna', key: 'home' },
    { href: '/blog', label: 'Blog', key: 'blog' },
    { 
      href: '/program', 
      label: 'Program', 
      key: 'program',
      submenu: [
        { href: '/program?destination=bali', label: 'Bali - 12 dni' },
        { href: '/program?destination=srilanka', label: 'Sri Lanka - 10 dni' }
      ]
    },
    { href: '/zajecia-online', label: 'Zajęcia Online', key: 'online' },
    { href: '/o-mnie', label: 'O mnie', key: 'about' },
    { href: '/galeria', label: 'Galeria', key: 'gallery' },
    { href: '/kontakt', label: 'Kontakt', key: 'contact' },
  ];

  useEffect(() => {
    let scrollTimeout;
    
    const handleScroll = () => {
      const scrollY = window.scrollY;
      
      // Show navbar after scrolling 100px
      if (scrollY > 100 && !isVisible) {
        setIsVisible(true);
      }
      
      // Add background when scrolled
      setIsScrolled(scrollY > 50);
      
      // Hide navbar when scrolling up quickly
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        if (scrollY < 100) {
          setIsVisible(false);
        }
      }, 150);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, [isVisible]);

  // Morphing logo animation
  const LogoMorph = () => (
    <Link href="/" className="flex items-center group">
      <motion.div
        whileHover={{ scale: 1.05 }}
        className="relative"
      >
        {/* Lotus position icon */}
        <motion.div
          initial={{ opacity: 1, scale: 1 }}
          whileHover={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            className="text-temple"
          >
            <path
              d="M16 8C16 8 12 12 12 16C12 18 14 20 16 20C18 20 20 18 20 16C20 12 16 8 16 8Z"
              fill="currentColor"
              opacity="0.6"
            />
            <path
              d="M16 24C16 24 20 20 20 16C20 14 18 12 16 12C14 12 12 14 12 16C12 20 16 24 16 24Z"
              fill="currentColor"
              opacity="0.6"
            />
            <circle cx="16" cy="16" r="2" fill="currentColor" />
          </svg>
        </motion.div>

        {/* Text logo */}
        <motion.span
          initial={{ opacity: 0, scale: 1.2 }}
          whileHover={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="text-2xl font-serif font-light text-temple tracking-wider"
        >
          BAKASANA
        </motion.span>
      </motion.div>
    </Link>
  );

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.header
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            duration: 0.6 
          }}
          className={`fixed top-0 left-0 right-0 z-40 transition-all duration-500 ${
            isScrolled 
              ? 'bg-dawn-mist/95 backdrop-blur-md border-b border-temple/10 shadow-soft' 
              : 'bg-transparent'
          }`}
        >
          <div className="container mx-auto px-4 h-20">
            <div className="flex items-center justify-between h-full">
              <LogoMorph />

              {/* Desktop Navigation */}
              <nav className="hidden md:flex items-center space-x-8">
                {navigationLinks.map((link) => (
                  <div key={link.key} className="relative group">
                    <Link
                      href={link.href}
                      className={`relative px-6 py-2 text-sm font-light tracking-wide uppercase transition-all duration-300 ${
                        pathname === link.href
                          ? 'text-temple'
                          : 'text-temple/70 hover:text-temple'
                      }`}
                      onMouseEnter={() => setActiveItem(link.key)}
                      onMouseLeave={() => setActiveItem('')}
                    >
                      {link.label}
                      
                      {/* Golden aura effect */}
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ 
                          opacity: activeItem === link.key || pathname === link.href ? 1 : 0,
                          scale: activeItem === link.key || pathname === link.href ? 1 : 0.8,
                        }}
                        transition={{ duration: 0.3 }}
                        className="absolute inset-0 bg-golden/10 rounded-full blur-sm -z-10"
                      />
                      
                      {/* Active indicator */}
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ 
                          width: activeItem === link.key || pathname === link.href ? '100%' : 0 
                        }}
                        transition={{ duration: 0.3 }}
                        className="absolute bottom-0 left-0 h-0.5 bg-temple"
                      />
                    </Link>

                    {/* Dropdown menu */}
                    {link.submenu && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ 
                          opacity: activeItem === link.key ? 1 : 0,
                          y: activeItem === link.key ? 0 : 10,
                        }}
                        transition={{ duration: 0.2 }}
                        className={`absolute top-full left-0 mt-2 py-2 bg-white/95 backdrop-blur-md rounded-xl border border-temple/10 shadow-medium min-w-48 ${
                          activeItem === link.key ? 'pointer-events-auto' : 'pointer-events-none'
                        }`}
                      >
                        {link.submenu.map((subItem, index) => (
                          <Link
                            key={index}
                            href={subItem.href}
                            className="block px-4 py-2 text-sm text-temple/80 hover:text-temple hover:bg-temple/5 transition-colors duration-200"
                          >
                            {subItem.label}
                          </Link>
                        ))}
                      </motion.div>
                    )}
                  </div>
                ))}
              </nav>

              {/* Mobile menu button */}
              <motion.button
                whileTap={{ scale: 0.95 }}
                className="md:hidden p-2 text-temple/70 hover:text-temple transition-colors duration-200"
                aria-label="Toggle menu"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </motion.button>
            </div>
          </div>
        </motion.header>
      )}
    </AnimatePresence>
  );
};

export default TranscendentNavbar;
