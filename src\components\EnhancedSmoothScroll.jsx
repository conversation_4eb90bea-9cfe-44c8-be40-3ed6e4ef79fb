'use client';

import React, { useEffect, useRef } from 'react';

const EnhancedSmoothScroll = ({ children }) => {
  const scrollContainerRef = useRef(null);
  const scrollTargetRef = useRef(0);
  const scrollCurrentRef = useRef(0);
  const easeRef = useRef(0.08); // Momentum-based easing
  const rafRef = useRef(null);

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    let isScrolling = false;
    let scrollTimeout;

    // Set initial height
    const updateHeight = () => {
      const height = scrollContainer.scrollHeight;
      document.body.style.height = `${height}px`;
    };

    // Smooth scroll animation
    const animate = () => {
      const diff = scrollTargetRef.current - scrollCurrentRef.current;
      const delta = Math.abs(diff);

      if (delta < 0.1) {
        scrollCurrentRef.current = scrollTargetRef.current;
        if (scrollContainer) {
          scrollContainer.style.transform = `translateY(-${scrollCurrentRef.current}px)`;
        }
        return;
      }

      scrollCurrentRef.current += diff * easeRef.current;
      
      if (scrollContainer) {
        scrollContainer.style.transform = `translateY(-${scrollCurrentRef.current}px)`;
      }

      rafRef.current = requestAnimationFrame(animate);
    };

    // Handle scroll events
    const handleScroll = () => {
      scrollTargetRef.current = window.pageYOffset;
      
      if (!isScrolling) {
        isScrolling = true;
        animate();
      }

      // Adjust easing based on scroll speed
      clearTimeout(scrollTimeout);
      easeRef.current = 0.12; // Faster during active scrolling
      
      scrollTimeout = setTimeout(() => {
        isScrolling = false;
        easeRef.current = 0.08; // Slower when scroll stops
      }, 100);
    };

    // Handle wheel events for momentum
    const handleWheel = (e) => {
      e.preventDefault();
      
      const delta = e.deltaY;
      const scrollSpeed = Math.abs(delta) > 100 ? 100 : Math.abs(delta);
      const direction = delta > 0 ? 1 : -1;
      
      scrollTargetRef.current += direction * scrollSpeed * 2;
      scrollTargetRef.current = Math.max(0, Math.min(scrollTargetRef.current, document.body.scrollHeight - window.innerHeight));
      
      window.scrollTo(0, scrollTargetRef.current);
    };

    // Handle touch events for mobile
    let touchStartY = 0;
    let touchCurrentY = 0;
    let touchVelocity = 0;
    let lastTouchTime = 0;

    const handleTouchStart = (e) => {
      touchStartY = e.touches[0].clientY;
      touchCurrentY = touchStartY;
      touchVelocity = 0;
      lastTouchTime = Date.now();
    };

    const handleTouchMove = (e) => {
      e.preventDefault();
      
      const currentTime = Date.now();
      const deltaTime = currentTime - lastTouchTime;
      const deltaY = e.touches[0].clientY - touchCurrentY;
      
      touchVelocity = deltaY / deltaTime;
      touchCurrentY = e.touches[0].clientY;
      lastTouchTime = currentTime;
      
      const scrollDelta = touchStartY - touchCurrentY;
      scrollTargetRef.current += scrollDelta * 2;
      scrollTargetRef.current = Math.max(0, Math.min(scrollTargetRef.current, document.body.scrollHeight - window.innerHeight));
      
      touchStartY = touchCurrentY;
      window.scrollTo(0, scrollTargetRef.current);
    };

    const handleTouchEnd = () => {
      // Apply momentum
      const momentum = touchVelocity * 500;
      scrollTargetRef.current -= momentum;
      scrollTargetRef.current = Math.max(0, Math.min(scrollTargetRef.current, document.body.scrollHeight - window.innerHeight));
      
      window.scrollTo(0, scrollTargetRef.current);
    };

    // Initialize
    updateHeight();
    scrollTargetRef.current = window.pageYOffset;
    scrollCurrentRef.current = window.pageYOffset;

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('touchstart', handleTouchStart, { passive: true });
    window.addEventListener('touchmove', handleTouchMove, { passive: false });
    window.addEventListener('touchend', handleTouchEnd, { passive: true });
    window.addEventListener('resize', updateHeight);

    // Observer for height changes
    const resizeObserver = new ResizeObserver(updateHeight);
    resizeObserver.observe(scrollContainer);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchmove', handleTouchMove);
      window.removeEventListener('touchend', handleTouchEnd);
      window.removeEventListener('resize', updateHeight);
      
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      
      resizeObserver.disconnect();
      document.body.style.height = 'auto';
    };
  }, []);

  return (
    <div
      ref={scrollContainerRef}
      className="fixed top-0 left-0 w-full will-change-transform"
      style={{
        transform: 'translateY(0px)',
      }}
    >
      {children}
    </div>
  );
};

export default EnhancedSmoothScroll;
