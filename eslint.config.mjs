/**
 * 🔧 PROFESSIONAL ESLINT CONFIGURATION
 * Optimized for Next.js 15 & React 19
 * 
 * ✅ Next.js Core Web Vitals
 * ✅ React Best Practices
 * ✅ TypeScript Support
 * ✅ Accessibility Rules
 * ✅ Performance Optimizations
 * ✅ Code Quality Standards
 */

import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends(
    "next/core-web-vitals",
    "next/typescript"
  ),
  {
    ignores: [
      ".next/**",
      "out/**",
      "dist/**",
      "build/**",
      "node_modules/**",
      "public/**",
      "**/*.min.js",
      "**/*.min.css"
    ],
    rules: {
      // Performance optimizations
      "react/no-unescaped-entities": "off",
      "react/jsx-key": "error",
      "react/no-array-index-key": "warn",
      "react/no-unused-prop-types": "off",
      "react/no-unused-state": "off",
      
      // Code quality - more lenient for production
      "prefer-const": "warn",
      "no-unused-vars": "warn",
      "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
      "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
      
      // Next.js specific
      "@next/next/no-img-element": "error",
      "@next/next/no-html-link-for-pages": "error",
      "@next/next/no-sync-scripts": "error",
      
      // Accessibility - keep important ones
      "jsx-a11y/alt-text": "warn",
      "jsx-a11y/aria-props": "warn",
      "jsx-a11y/aria-proptypes": "warn",
      "jsx-a11y/aria-unsupported-elements": "warn",
      "jsx-a11y/role-has-required-aria-props": "warn",
      "jsx-a11y/role-supports-aria-props": "warn",
      
      // TypeScript - more lenient
      "@typescript-eslint/no-unused-vars": "warn",
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/prefer-as-const": "warn",
    },
    settings: {
      next: {
        rootDir: ["./src/"],
      },
    },
  },
];

export default eslintConfig;
