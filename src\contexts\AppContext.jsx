'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Initial state
const initialState = {
  isLoading: false,
  user: null,
  theme: 'light',
  mood: null,
  isHydrated: false,
  error: null,
  bookingStep: 1,
  selectedProgram: null,
  contactForm: {
    name: '',
    email: '',
    message: ''
  }
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_THEME: 'SET_THEME',
  SET_MOOD: 'SET_MOOD',
  SET_HYDRATED: 'SET_HYDRATED',
  SET_ERROR: 'SET_ERROR',
  SET_BOOKING_STEP: 'SET_BOOKING_STEP',
  SET_SELECTED_PROGRAM: 'SET_SELECTED_PROGRAM',
  UPDATE_CONTACT_FORM: 'UPDATE_CONTACT_FORM',
  RESET_CONTACT_FORM: 'RESET_CONTACT_FORM',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Reducer
function appReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, isLoading: action.payload };
    case ActionTypes.SET_USER:
      return { ...state, user: action.payload };
    case ActionTypes.SET_THEME:
      return { ...state, theme: action.payload };
    case ActionTypes.SET_MOOD:
      return { ...state, mood: action.payload };
    case ActionTypes.SET_HYDRATED:
      return { ...state, isHydrated: action.payload };
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload };
    case ActionTypes.SET_BOOKING_STEP:
      return { ...state, bookingStep: action.payload };
    case ActionTypes.SET_SELECTED_PROGRAM:
      return { ...state, selectedProgram: action.payload };
    case ActionTypes.UPDATE_CONTACT_FORM:
      return { 
        ...state, 
        contactForm: { ...state.contactForm, ...action.payload } 
      };
    case ActionTypes.RESET_CONTACT_FORM:
      return { 
        ...state, 
        contactForm: initialState.contactForm 
      };
    case ActionTypes.CLEAR_ERROR:
      return { ...state, error: null };
    default:
      return state;
  }
}

// Context
const AppContext = createContext();

// Custom hook
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Provider component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Actions
  const actions = {
    setLoading: (loading) => dispatch({ type: ActionTypes.SET_LOADING, payload: loading }),
    setUser: (user) => dispatch({ type: ActionTypes.SET_USER, payload: user }),
    setTheme: (theme) => dispatch({ type: ActionTypes.SET_THEME, payload: theme }),
    setMood: (mood) => dispatch({ type: ActionTypes.SET_MOOD, payload: mood }),
    setHydrated: (hydrated) => dispatch({ type: ActionTypes.SET_HYDRATED, payload: hydrated }),
    setError: (error) => dispatch({ type: ActionTypes.SET_ERROR, payload: error }),
    clearError: () => dispatch({ type: ActionTypes.CLEAR_ERROR }),
    setBookingStep: (step) => dispatch({ type: ActionTypes.SET_BOOKING_STEP, payload: step }),
    setSelectedProgram: (program) => dispatch({ type: ActionTypes.SET_SELECTED_PROGRAM, payload: program }),
    updateContactForm: (formData) => dispatch({ type: ActionTypes.UPDATE_CONTACT_FORM, payload: formData }),
    resetContactForm: () => dispatch({ type: ActionTypes.RESET_CONTACT_FORM })
  };

  // Effect for hydration
  useEffect(() => {
    actions.setHydrated(true);
  }, []);

  // Effect for loading saved data
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        // Load saved theme
        const savedTheme = localStorage.getItem('bakasana-theme');
        if (savedTheme) {
          actions.setTheme(savedTheme);
        }

        // Load saved mood
        const savedMood = localStorage.getItem('bakasana-mood');
        if (savedMood) {
          actions.setMood(savedMood);
        }

        // Load saved program
        const savedProgram = localStorage.getItem('bakasana-selected-program');
        if (savedProgram) {
          try {
            const program = JSON.parse(savedProgram);
            actions.setSelectedProgram(program);
          } catch (err) {
            console.warn('Failed to parse saved program:', err);
          }
        }
      } catch (error) {
        console.warn('Failed to load saved data:', error);
      }
    }
  }, []);

  // Effect for saving data
  useEffect(() => {
    if (state.isHydrated && typeof window !== 'undefined') {
      try {
        if (state.theme) {
          localStorage.setItem('bakasana-theme', state.theme);
        }
        if (state.mood) {
          localStorage.setItem('bakasana-mood', state.mood);
        }
        if (state.selectedProgram) {
          localStorage.setItem('bakasana-selected-program', JSON.stringify(state.selectedProgram));
        }
      } catch (error) {
        console.warn('Failed to save data:', error);
      }
    }
  }, [state.theme, state.mood, state.selectedProgram, state.isHydrated]);

  const value = {
    state,
    actions,
    ...actions // Spread actions for easier access
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

export default AppContext;